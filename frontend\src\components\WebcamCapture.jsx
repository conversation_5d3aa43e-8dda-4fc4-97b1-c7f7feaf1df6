import { useState, useRef, useEffect } from 'react'

const WebcamCapture = () => {
  const videoRef = useRef(null)
  const canvasRef = useRef(null)
  const [isStreaming, setIsStreaming] = useState(false)
  const [capturedImage, setCapturedImage] = useState(null)
  const [detectionMode, setDetectionMode] = useState('detection')
  const [detectionMethod, setDetectionMethod] = useState('haar') // Use faster method for real-time
  const [error, setError] = useState(null)
  const [processing, setProcessing] = useState(false)
  const [results, setResults] = useState(null)

  useEffect(() => {
    return () => {
      stopWebcam()
    }
  }, [])

  const startWebcam = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user'
        }
      })
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        setIsStreaming(true)
        setError(null)
      }
    } catch (err) {
      setError('Failed to access webcam. Please ensure you have granted camera permissions.')
    }
  }

  const stopWebcam = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks()
      tracks.forEach(track => track.stop())
      videoRef.current.srcObject = null
    }
    setIsStreaming(false)
  }

  const captureImage = () => {
    if (!videoRef.current || !canvasRef.current) return

    const video = videoRef.current
    const canvas = canvasRef.current
    const context = canvas.getContext('2d')

    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    context.drawImage(video, 0, 0, canvas.width, canvas.height)
    
    const imageData = canvas.toDataURL('image/jpeg', 0.8)
    setCapturedImage(imageData)
    setResults(null)
  }

  const processImage = async () => {
    if (!capturedImage) return

    setProcessing(true)
    setError(null)

    try {
      const endpoint = detectionMode === 'detection' ? '/api/detect' : '/api/recognize'
      const payload = {
        image: capturedImage,
        ...(detectionMode === 'detection' 
          ? { method: detectionMethod }
          : { tolerance: 0.6 }
        )
      }

      const response = await fetch(`http://localhost:5000${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (data.success) {
        setResults(data)
      } else {
        setError(data.error || 'Processing failed')
      }
    } catch (err) {
      setError('Failed to connect to the server')
    } finally {
      setProcessing(false)
    }
  }

  const clearCapture = () => {
    setCapturedImage(null)
    setResults(null)
    setError(null)
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="card">
        <h2 className="text-xl font-semibold mb-4">Live Camera Capture</h2>
        
        <div className="space-y-4">
          {/* Mode Selection */}
          <div>
            <label className="form-label">Processing Mode</label>
            <div className="flex gap-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="detection"
                  checked={detectionMode === 'detection'}
                  onChange={(e) => setDetectionMode(e.target.value)}
                  className="mr-2"
                />
                Face Detection
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="recognition"
                  checked={detectionMode === 'recognition'}
                  onChange={(e) => setDetectionMode(e.target.value)}
                  className="mr-2"
                />
                Face Recognition
              </label>
            </div>
          </div>

          {/* Detection Method (only for detection mode) */}
          {detectionMode === 'detection' && (
            <div>
              <label className="form-label">Detection Method</label>
              <select
                value={detectionMethod}
                onChange={(e) => setDetectionMethod(e.target.value)}
                className="form-input"
              >
                <option value="haar">Haar Cascades (Fastest)</option>
                <option value="dlib">Dlib HOG (Balanced)</option>
                <option value="mtcnn">MTCNN (Most Accurate)</option>
              </select>
            </div>
          )}

          {/* Camera Controls */}
          <div className="flex gap-2">
            {!isStreaming ? (
              <button onClick={startWebcam} className="btn btn-primary">
                📹 Start Camera
              </button>
            ) : (
              <>
                <button onClick={stopWebcam} className="btn btn-danger">
                  ⏹️ Stop Camera
                </button>
                <button onClick={captureImage} className="btn btn-success">
                  📸 Capture Photo
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="card bg-red-50 border-red-200">
          <div className="flex items-center">
            <span className="text-red-500 mr-2">❌</span>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Camera Feed */}
      {isStreaming && (
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">Live Camera Feed</h3>
          <div className="flex justify-center">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="rounded-lg shadow-lg max-w-full"
              style={{ maxHeight: '400px' }}
            />
          </div>
        </div>
      )}

      {/* Hidden canvas for image capture */}
      <canvas ref={canvasRef} style={{ display: 'none' }} />

      {/* Captured Image */}
      {capturedImage && (
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Captured Image</h3>
            <div className="flex gap-2">
              <button
                onClick={processImage}
                disabled={processing}
                className="btn btn-primary"
              >
                {processing ? (
                  <>
                    <div className="spinner mr-2"></div>
                    Processing...
                  </>
                ) : (
                  `Process with ${detectionMode === 'detection' ? 'Detection' : 'Recognition'}`
                )}
              </button>
              <button onClick={clearCapture} className="btn btn-secondary">
                Clear
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Original Image */}
            <div>
              <h4 className="font-medium mb-2">Original</h4>
              <img
                src={capturedImage}
                alt="Captured"
                className="w-full rounded-lg shadow"
              />
            </div>

            {/* Processed Image */}
            {results && (
              <div>
                <h4 className="font-medium mb-2">Processed</h4>
                <img
                  src={results.result_image}
                  alt="Processed"
                  className="w-full rounded-lg shadow"
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Results */}
      {results && (
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">
            {detectionMode === 'detection' ? 'Detection' : 'Recognition'} Results
          </h3>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {detectionMode === 'detection' ? results.face_count : results.face_count}
                </div>
                <div className="text-sm text-gray-600">Faces Found</div>
              </div>
              
              {detectionMode === 'recognition' && (
                <>
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {results.recognitions?.filter(r => r.name !== 'Unknown').length || 0}
                    </div>
                    <div className="text-sm text-gray-600">Recognized</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-600">
                      {results.recognitions?.filter(r => r.name === 'Unknown').length || 0}
                    </div>
                    <div className="text-sm text-gray-600">Unknown</div>
                  </div>
                </>
              )}
              
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {detectionMode === 'detection' ? results.method_used?.toUpperCase() : 'AI'}
                </div>
                <div className="text-sm text-gray-600">Method</div>
              </div>
            </div>

            {/* Detailed Results */}
            {detectionMode === 'recognition' && results.recognitions && results.recognitions.length > 0 && (
              <div className="mt-4 space-y-2">
                <h4 className="font-medium">Recognized Faces:</h4>
                {results.recognitions.map((recognition, index) => (
                  <div key={index} className="flex items-center justify-between bg-white p-2 rounded">
                    <span className={recognition.name === 'Unknown' ? 'text-red-600' : 'text-green-600'}>
                      {recognition.name === 'Unknown' ? '❓ Unknown Person' : `👤 ${recognition.name}`}
                    </span>
                    {recognition.confidence > 0 && (
                      <span className="text-sm text-gray-600">
                        {(recognition.confidence * 100).toFixed(1)}% confidence
                      </span>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="card bg-blue-50 border-blue-200">
        <h3 className="font-medium text-blue-800 mb-2">📋 Instructions</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Click "Start Camera" to begin the webcam feed</li>
          <li>• Position your face clearly in the camera view</li>
          <li>• Click "Capture Photo" to take a snapshot</li>
          <li>• Choose between face detection or recognition mode</li>
          <li>• Process the captured image to see results</li>
          <li>• For recognition, ensure you have added known faces first</li>
        </ul>
      </div>
    </div>
  )
}

export default WebcamCapture
