#!/usr/bin/env python3
"""
Test face detection with real face images
Downloads sample images and tests the face detection API
"""

import cv2
import numpy as np
import base64
import requests
import json
import os
from urllib.request import urlretrieve
import urllib.error

def download_sample_images():
    """Download sample face images for testing"""
    print("📥 Downloading sample face images...")
    
    # Sample images (using placeholder/demo images)
    sample_urls = [
        {
            'name': 'person1.jpg',
            'url': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
            'description': 'Professional headshot'
        },
        {
            'name': 'group.jpg', 
            'url': 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=600&h=400&fit=crop',
            'description': 'Group photo'
        }
    ]
    
    downloaded_files = []
    
    for img_info in sample_urls:
        try:
            filename = f"sample_{img_info['name']}"
            if not os.path.exists(filename):
                print(f"  Downloading {img_info['description']}...")
                urlretrieve(img_info['url'], filename)
                print(f"  ✅ Saved as {filename}")
            else:
                print(f"  ✅ {filename} already exists")
            downloaded_files.append(filename)
        except urllib.error.URLError as e:
            print(f"  ❌ Failed to download {img_info['name']}: {e}")
        except Exception as e:
            print(f"  ❌ Error downloading {img_info['name']}: {e}")
    
    return downloaded_files

def create_realistic_test_image():
    """Create a more realistic test image using OpenCV drawing functions"""
    print("🎨 Creating realistic test face image...")
    
    # Create a larger image
    img = np.ones((500, 400, 3), dtype=np.uint8) * 240  # Light gray background
    
    # Face oval
    center = (200, 250)
    axes = (80, 100)
    cv2.ellipse(img, center, axes, 0, 0, 360, (220, 180, 160), -1)  # Skin color
    cv2.ellipse(img, center, axes, 0, 0, 360, (200, 160, 140), 2)   # Face outline
    
    # Hair
    hair_points = np.array([
        [120, 180], [280, 180], [290, 160], [280, 140], 
        [260, 120], [240, 110], [200, 105], [160, 110], 
        [140, 120], [120, 140], [110, 160]
    ], np.int32)
    cv2.fillPoly(img, [hair_points], (101, 67, 33))  # Brown hair
    
    # Eyes
    # Left eye
    cv2.ellipse(img, (170, 220), (15, 8), 0, 0, 360, (255, 255, 255), -1)  # Eye white
    cv2.ellipse(img, (170, 220), (8, 8), 0, 0, 360, (139, 69, 19), -1)     # Iris
    cv2.ellipse(img, (170, 220), (4, 4), 0, 0, 360, (0, 0, 0), -1)         # Pupil
    
    # Right eye
    cv2.ellipse(img, (230, 220), (15, 8), 0, 0, 360, (255, 255, 255), -1)  # Eye white
    cv2.ellipse(img, (230, 220), (8, 8), 0, 0, 360, (139, 69, 19), -1)     # Iris
    cv2.ellipse(img, (230, 220), (4, 4), 0, 0, 360, (0, 0, 0), -1)         # Pupil
    
    # Eyebrows
    cv2.ellipse(img, (170, 205), (20, 5), 0, 0, 180, (101, 67, 33), -1)
    cv2.ellipse(img, (230, 205), (20, 5), 0, 0, 180, (101, 67, 33), -1)
    
    # Nose
    nose_points = np.array([[200, 240], [195, 260], [200, 265], [205, 260]], np.int32)
    cv2.fillPoly(img, [nose_points], (200, 160, 140))
    cv2.polylines(img, [nose_points], True, (180, 140, 120), 1)
    
    # Mouth
    cv2.ellipse(img, (200, 290), (20, 8), 0, 0, 180, (180, 100, 100), -1)  # Mouth
    cv2.ellipse(img, (200, 290), (20, 8), 0, 0, 180, (160, 80, 80), 2)     # Mouth outline
    
    # Add some facial features for better detection
    # Cheeks
    cv2.ellipse(img, (150, 260), (15, 10), 0, 0, 360, (210, 170, 150), -1)
    cv2.ellipse(img, (250, 260), (15, 10), 0, 0, 360, (210, 170, 150), -1)
    
    # Chin
    cv2.ellipse(img, (200, 320), (30, 20), 0, 0, 180, (210, 170, 150), -1)
    
    filename = 'realistic_test_face.jpg'
    cv2.imwrite(filename, img)
    print(f"✅ Realistic test image saved as '{filename}'")
    
    return filename

def image_to_base64(image_path):
    """Convert image file to base64 string"""
    with open(image_path, 'rb') as image_file:
        image_data = image_file.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        return f"data:image/jpeg;base64,{image_base64}"

def test_face_detection_with_image(image_path, description=""):
    """Test face detection with a specific image"""
    print(f"\n🔍 Testing face detection on: {image_path}")
    if description:
        print(f"   Description: {description}")
    
    try:
        # Convert image to base64
        image_base64 = image_to_base64(image_path)
        
        # Test with Haar cascade (most reliable)
        payload = {
            'image': image_base64,
            'method': 'haar'
        }
        
        response = requests.post(
            'http://localhost:5000/api/detect',
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data['success']:
                face_count = data['face_count']
                print(f"✅ Detection Results:")
                print(f"   - Faces detected: {face_count}")
                print(f"   - Method: {data['method_used']}")
                
                if face_count > 0:
                    print(f"   - Detection details:")
                    for i, detection in enumerate(data.get('detections', [])):
                        bbox = detection['bbox']
                        confidence = detection['confidence']
                        print(f"     Face {i+1}: bbox={bbox}, confidence={confidence:.2f}")
                    
                    # Save result image
                    if 'result_image' in data:
                        result_filename = f"result_{os.path.basename(image_path)}"
                        image_data = data['result_image'].split(',')[1] if ',' in data['result_image'] else data['result_image']
                        image_bytes = base64.b64decode(image_data)
                        
                        with open(result_filename, 'wb') as f:
                            f.write(image_bytes)
                        print(f"   - Result with bounding boxes saved as '{result_filename}'")
                else:
                    print("   - No faces detected in this image")
                    
            else:
                print(f"❌ Detection failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing image: {e}")

def main():
    """Main test function"""
    print("🚀 Real Face Detection Test Suite")
    print("=" * 60)
    
    # Check API health
    try:
        response = requests.get('http://localhost:5000/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ Backend API is running")
        else:
            print("❌ Backend API health check failed")
            return
    except:
        print("❌ Cannot connect to backend API. Please start the server first:")
        print("   cd backend && python app.py")
        return
    
    # Test with realistic drawn face
    realistic_image = create_realistic_test_image()
    test_face_detection_with_image(realistic_image, "Realistic drawn face")
    
    # Try to download and test real images
    print(f"\n{'='*60}")
    downloaded_images = download_sample_images()
    
    for image_path in downloaded_images:
        if os.path.exists(image_path):
            test_face_detection_with_image(image_path, "Real photograph")
    
    print(f"\n{'='*60}")
    print("🎯 Test Complete!")
    print("\n📋 Next Steps:")
    print("1. Open demo.html in your browser")
    print("2. Try the 'Load Sample Face Image' button")
    print("3. Or upload your own photos with faces")
    print("4. Test different detection methods")
    print("\n💡 Tips:")
    print("- Use clear, well-lit photos for best results")
    print("- Front-facing faces work better than profile shots")
    print("- Larger faces (>50x50 pixels) detect more reliably")

if __name__ == "__main__":
    main()
