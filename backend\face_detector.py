import cv2
import numpy as np
from typing import List, <PERSON><PERSON>, Dict, Any
import base64
from PIL import Image
import io

class FaceDetector:
    def __init__(self):
        # Initialize Haar cascade classifier
        try:
            self.haar_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        except:
            # Fallback path for different OpenCV installations
            self.haar_cascade = cv2.CascadeClassifier('haarcascade_frontalface_default.xml')

        # Detection methods (only Haar for now)
        self.detection_methods = {
            'haar': self._detect_faces_haar,
            'mtcnn': self._detect_faces_haar,  # Fallback to Haar
            'dlib': self._detect_faces_haar   # Fallback to Haar
        }

    def detect_faces(self, image: np.ndarray, method: str = 'mtcnn') -> List[Dict[str, Any]]:
        """
        Detect faces in an image using the specified method

        Args:
            image: Input image as numpy array
            method: Detection method ('haar', 'mtcnn', 'dlib')

        Returns:
            List of face detection results with bounding boxes and confidence
        """
        if method not in self.detection_methods:
            raise ValueError(f"Unknown detection method: {method}")

        return self.detection_methods[method](image)

    def _detect_faces_haar(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect faces using Haar cascades"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.haar_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(30, 30)
        )

        results = []
        for (x, y, w, h) in faces:
            results.append({
                'bbox': [x, y, x + w, y + h],
                'confidence': 1.0,  # Haar doesn't provide confidence
                'method': 'haar'
            })

        return results

    def _detect_faces_mtcnn(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect faces using MTCNN (fallback to Haar)"""
        return self._detect_faces_haar(image)

    def _detect_faces_dlib(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect faces using dlib (fallback to Haar)"""
        return self._detect_faces_haar(image)

    def draw_faces(self, image: np.ndarray, detections: List[Dict[str, Any]]) -> np.ndarray:
        """Draw bounding boxes around detected faces"""
        result_image = image.copy()

        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            method = detection['method']

            x1, y1, x2, y2 = bbox

            # Choose color based on method
            colors = {
                'haar': (255, 0, 0),    # Blue
                'mtcnn': (0, 255, 0),   # Green
                'dlib': (0, 0, 255)     # Red
            }
            color = colors.get(method, (255, 255, 255))

            # Draw bounding box
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)

            # Draw confidence and method
            label = f"{method}: {confidence:.2f}"
            cv2.putText(result_image, label, (x1, y1 - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # Draw keypoints if available (MTCNN)
            if 'keypoints' in detection:
                keypoints = detection['keypoints']
                for point_name, (x, y) in keypoints.items():
                    cv2.circle(result_image, (int(x), int(y)), 2, color, -1)

        return result_image

    def process_video_frame(self, frame: np.ndarray, method: str = 'haar') -> Tuple[np.ndarray, List[Dict[str, Any]]]:
        """Process a single video frame for face detection"""
        detections = self.detect_faces(frame, method)
        result_frame = self.draw_faces(frame, detections)
        return result_frame, detections

    @staticmethod
    def encode_image_to_base64(image: np.ndarray) -> str:
        """Convert numpy image to base64 string"""
        _, buffer = cv2.imencode('.jpg', image)
        image_base64 = base64.b64encode(buffer).decode('utf-8')
        return image_base64

    @staticmethod
    def decode_base64_to_image(base64_string: str) -> np.ndarray:
        """Convert base64 string to numpy image"""
        image_data = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_data))
        return cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
