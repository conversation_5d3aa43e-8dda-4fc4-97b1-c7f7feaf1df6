#!/usr/bin/env python3
"""
Simple test for face detection API
"""

import cv2
import numpy as np
import base64
import requests
import json

def create_simple_image():
    """Create a simple test image"""
    # Create a 200x200 white image
    img = np.ones((200, 200, 3), dtype=np.uint8) * 255
    
    # Add some simple content
    cv2.rectangle(img, (50, 50), (150, 150), (200, 200, 200), -1)
    cv2.circle(img, (100, 100), 30, (150, 150, 150), -1)
    
    return img

def test_simple_detection():
    """Test with a very simple image"""
    print("🧪 Simple Face Detection Test")
    print("=" * 40)
    
    # Create simple image
    img = create_simple_image()
    
    # Save for reference
    cv2.imwrite('simple_test.jpg', img)
    print("✅ Simple test image created")
    
    # Convert to base64
    _, buffer = cv2.imencode('.jpg', img)
    img_base64 = base64.b64encode(buffer).decode('utf-8')
    img_data_url = f"data:image/jpeg;base64,{img_base64}"
    
    print(f"Base64 length: {len(img_base64)}")
    print(f"First 50 chars: {img_base64[:50]}...")
    
    # Test API
    try:
        payload = {
            'image': img_data_url,
            'method': 'haar'
        }
        
        print("Sending request to API...")
        response = requests.post(
            'http://localhost:5000/api/detect',
            json=payload,
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API call successful!")
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ API call failed")
            print(f"Response text: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_simple_detection()
