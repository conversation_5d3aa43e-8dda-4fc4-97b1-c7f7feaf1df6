import { useState } from 'react'
import ImageUpload from './components/ImageUpload'
import FaceDetection from './components/FaceDetection'
import FaceRecognition from './components/FaceRecognition'
import KnownFaces from './components/KnownFaces'
import WebcamCapture from './components/WebcamCapture'

function App() {
  const [activeTab, setActiveTab] = useState('detection')

  const tabs = [
    { id: 'detection', label: 'Face Detection', icon: '🔍' },
    { id: 'recognition', label: 'Face Recognition', icon: '👤' },
    { id: 'webcam', label: 'Live Camera', icon: '📹' },
    { id: 'manage', label: 'Manage Faces', icon: '⚙️' }
  ]

  const renderActiveComponent = () => {
    switch (activeTab) {
      case 'detection':
        return <FaceDetection />
      case 'recognition':
        return <FaceRecognition />
      case 'webcam':
        return <WebcamCapture />
      case 'manage':
        return <KnownFaces />
      default:
        return <FaceDetection />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="container py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="text-3xl">🤖</div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">
                  AI Face Detection & Recognition
                </h1>
                <p className="text-gray-600">
                  Advanced computer vision powered by deep learning
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="container">
          <div className="flex gap-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 font-medium text-sm transition ${
                  activeTab === tab.id
                    ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="container py-6">
        {renderActiveComponent()}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="container py-6">
          <div className="text-center text-gray-600">
            <p>
              Built with React, OpenCV, MTCNN, and face_recognition library
            </p>
            <p className="text-sm mt-2">
              Supports Haar Cascades, MTCNN, and dlib face detection methods
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
