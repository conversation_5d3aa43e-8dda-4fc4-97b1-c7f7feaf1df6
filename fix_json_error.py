#!/usr/bin/env python3
"""
Fix for JSON serialization error in face detection
Solves: "Object of type int32 is not JSON serializable"
"""

import json
import numpy as np
from typing import Any, Dict, List

class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles NumPy data types"""
    
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.bool_):
            return bool(obj)
        return super(NumpyEncoder, self).default(obj)

def convert_numpy_types(data):
    """
    Recursively convert NumPy types to Python native types
    This ensures JSON serialization works properly
    """
    if isinstance(data, dict):
        return {key: convert_numpy_types(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif isinstance(data, np.bool_):
        return bool(data)
    else:
        return data

def test_json_serialization():
    """Test the JSON serialization fix"""
    print("🧪 Testing JSON Serialization Fix")
    print("=" * 50)
    
    # Create test data with NumPy types (similar to face detection results)
    test_data = {
        'success': True,
        'face_count': np.int32(2),  # This would cause the error
        'detections': [
            {
                'bbox': [np.int32(100), np.int32(150), np.int32(200), np.int32(250)],
                'confidence': np.float64(0.95),
                'method': 'haar'
            },
            {
                'bbox': [np.int32(300), np.int32(100), np.int32(400), np.int32(200)],
                'confidence': np.float64(0.87),
                'method': 'haar'
            }
        ],
        'method_used': 'haar'
    }
    
    print("Original data with NumPy types:")
    print(f"face_count type: {type(test_data['face_count'])}")
    print(f"bbox[0] type: {type(test_data['detections'][0]['bbox'][0])}")
    print(f"confidence type: {type(test_data['detections'][0]['confidence'])}")
    
    # Test 1: Try normal JSON serialization (this would fail)
    try:
        json.dumps(test_data)
        print("❌ Normal JSON serialization should have failed!")
    except TypeError as e:
        print(f"✅ Expected error caught: {e}")
    
    # Test 2: Use custom encoder
    try:
        json_string = json.dumps(test_data, cls=NumpyEncoder)
        print("✅ Custom encoder works!")
        print(f"JSON length: {len(json_string)} characters")
    except Exception as e:
        print(f"❌ Custom encoder failed: {e}")
    
    # Test 3: Use conversion function
    try:
        converted_data = convert_numpy_types(test_data)
        json_string = json.dumps(converted_data)
        print("✅ Conversion function works!")
        print(f"JSON length: {len(json_string)} characters")
        
        print("\nConverted data types:")
        print(f"face_count type: {type(converted_data['face_count'])}")
        print(f"bbox[0] type: {type(converted_data['detections'][0]['bbox'][0])}")
        print(f"confidence type: {type(converted_data['detections'][0]['confidence'])}")
        
    except Exception as e:
        print(f"❌ Conversion function failed: {e}")

def create_fixed_backend_code():
    """Create the fixed backend code"""
    print("\n🔧 Creating Fixed Backend Code")
    print("=" * 50)
    
    fixed_code = '''
# Add this to the top of backend/app.py after other imports
import numpy as np

def convert_numpy_types(data):
    """Convert NumPy types to Python native types for JSON serialization"""
    if isinstance(data, dict):
        return {key: convert_numpy_types(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif isinstance(data, np.bool_):
        return bool(data)
    else:
        return data

# In the detect_faces() function, before returning the JSON response:
# Replace this line:
#   return jsonify({
#       'success': True,
#       'detections': detections,
#       'result_image': result_base64,
#       'face_count': len(detections),
#       'method_used': method
#   })

# With this:
response_data = {
    'success': True,
    'detections': detections,
    'result_image': result_base64,
    'face_count': len(detections),
    'method_used': method
}

# Convert NumPy types to Python types
response_data = convert_numpy_types(response_data)

return jsonify(response_data)
'''
    
    with open('backend_fix.txt', 'w') as f:
        f.write(fixed_code)
    
    print("✅ Fixed backend code saved to 'backend_fix.txt'")
    print("📋 Instructions:")
    print("1. Copy the convert_numpy_types function to backend/app.py")
    print("2. Update the detect_faces() function as shown")
    print("3. Restart the backend server")

def apply_fix_to_backend():
    """Apply the fix directly to the backend file"""
    print("\n🔨 Applying Fix to Backend")
    print("=" * 50)
    
    try:
        # Read the current backend file
        with open('backend/app.py', 'r') as f:
            content = f.read()
        
        # Check if fix is already applied
        if 'convert_numpy_types' in content:
            print("✅ Fix already applied to backend/app.py")
            return True
        
        # Add the conversion function after imports
        import_section = "from face_recognizer import FaceRecognizer"
        if import_section in content:
            conversion_function = '''
def convert_numpy_types(data):
    """Convert NumPy types to Python native types for JSON serialization"""
    if isinstance(data, dict):
        return {key: convert_numpy_types(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif isinstance(data, np.bool_):
        return bool(data)
    else:
        return data
'''
            
            content = content.replace(
                import_section,
                import_section + conversion_function
            )
            
            # Fix the return statement in detect_faces function
            old_return = '''return jsonify({
            'success': True,
            'detections': detections,
            'result_image': result_base64,
            'face_count': len(detections),
            'method_used': method
        })'''
            
            new_return = '''response_data = {
            'success': True,
            'detections': detections,
            'result_image': result_base64,
            'face_count': len(detections),
            'method_used': method
        }
        
        # Convert NumPy types to Python types
        response_data = convert_numpy_types(response_data)
        
        return jsonify(response_data)'''
            
            if old_return in content:
                content = content.replace(old_return, new_return)
                
                # Write the fixed content back
                with open('backend/app.py', 'w') as f:
                    f.write(content)
                
                print("✅ Fix applied successfully to backend/app.py")
                print("🔄 Please restart the backend server for changes to take effect")
                return True
            else:
                print("⚠️ Could not find exact return statement to replace")
                print("📝 Manual fix required - see backend_fix.txt for instructions")
                return False
        else:
            print("⚠️ Could not find import section to modify")
            return False
            
    except Exception as e:
        print(f"❌ Error applying fix: {e}")
        return False

def main():
    """Main function to fix the JSON serialization error"""
    print("🚀 Face Detection JSON Serialization Fix")
    print("=" * 60)
    
    # Test the serialization issue and solution
    test_json_serialization()
    
    # Create fixed backend code
    create_fixed_backend_code()
    
    # Try to apply fix automatically
    if apply_fix_to_backend():
        print("\n" + "=" * 60)
        print("✅ JSON SERIALIZATION FIX APPLIED!")
        print("=" * 60)
        print("\n📋 Next Steps:")
        print("1. Restart the backend server:")
        print("   cd backend")
        print("   python app.py")
        print("2. Test the face detection in the browser")
        print("3. The error should now be resolved!")
    else:
        print("\n" + "=" * 60)
        print("⚠️ MANUAL FIX REQUIRED")
        print("=" * 60)
        print("\n📋 Manual Steps:")
        print("1. Open backend/app.py")
        print("2. Follow instructions in backend_fix.txt")
        print("3. Restart the backend server")
    
    print("\n💡 What This Fix Does:")
    print("• Converts NumPy int32/float64 to Python int/float")
    print("• Ensures all data is JSON serializable")
    print("• Maintains data integrity and functionality")
    print("• Prevents 'Object of type int32 is not JSON serializable' error")

if __name__ == "__main__":
    main()
