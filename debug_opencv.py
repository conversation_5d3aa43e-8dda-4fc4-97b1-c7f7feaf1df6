#!/usr/bin/env python3
"""
Debug OpenCV and Haar cascade loading
"""

import cv2
import numpy as np
import os

def test_opencv_installation():
    """Test OpenCV installation and Haar cascade availability"""
    print("🔍 Testing OpenCV Installation")
    print("=" * 50)
    
    # Check OpenCV version
    print(f"OpenCV Version: {cv2.__version__}")
    
    # Check if cv2.data exists
    if hasattr(cv2, 'data'):
        print(f"✅ cv2.data is available")
        if hasattr(cv2.data, 'haarcascades'):
            print(f"✅ cv2.data.haarcascades is available")
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            print(f"Cascade path: {cascade_path}")
            
            if os.path.exists(cascade_path):
                print(f"✅ Haar cascade file exists")
                
                # Try to load the cascade
                try:
                    cascade = cv2.CascadeClassifier(cascade_path)
                    if cascade.empty():
                        print("❌ Cascade loaded but is empty")
                    else:
                        print("✅ Haar cascade loaded successfully")
                        return cascade
                except Exception as e:
                    print(f"❌ Error loading cascade: {e}")
            else:
                print(f"❌ Haar cascade file does not exist at: {cascade_path}")
        else:
            print("❌ cv2.data.haarcascades not available")
    else:
        print("❌ cv2.data not available")
    
    # Try alternative paths
    print("\n🔍 Trying alternative cascade paths...")
    
    # Check OpenCV installation directory
    opencv_path = os.path.dirname(cv2.__file__)
    alt_paths = [
        os.path.join(opencv_path, 'data', 'haarcascade_frontalface_default.xml'),
        os.path.join(opencv_path, '..', 'data', 'haarcascades', 'haarcascade_frontalface_default.xml'),
        'haarcascade_frontalface_default.xml'  # Current directory
    ]
    
    for path in alt_paths:
        print(f"Checking: {path}")
        if os.path.exists(path):
            print(f"✅ Found cascade at: {path}")
            try:
                cascade = cv2.CascadeClassifier(path)
                if not cascade.empty():
                    print("✅ Alternative cascade loaded successfully")
                    return cascade
                else:
                    print("❌ Alternative cascade is empty")
            except Exception as e:
                print(f"❌ Error loading alternative cascade: {e}")
        else:
            print(f"❌ Not found: {path}")
    
    return None

def test_simple_detection():
    """Test face detection with a simple image"""
    print("\n🧪 Testing Simple Face Detection")
    print("=" * 50)
    
    cascade = test_opencv_installation()
    
    if cascade is None:
        print("❌ No working Haar cascade found")
        return False
    
    # Create a simple test image
    img = np.ones((300, 300, 3), dtype=np.uint8) * 255  # White image
    
    # Draw a simple face-like pattern
    cv2.circle(img, (150, 150), 50, (200, 200, 200), -1)  # Face
    cv2.circle(img, (130, 130), 5, (0, 0, 0), -1)        # Left eye
    cv2.circle(img, (170, 130), 5, (0, 0, 0), -1)        # Right eye
    cv2.ellipse(img, (150, 170), (10, 5), 0, 0, 180, (0, 0, 0), 2)  # Mouth
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Try detection
    try:
        faces = cascade.detectMultiScale(gray, 1.1, 5)
        print(f"Detection result: {len(faces)} faces found")
        
        if len(faces) > 0:
            print("✅ Face detection is working")
            for i, (x, y, w, h) in enumerate(faces):
                print(f"  Face {i+1}: x={x}, y={y}, w={w}, h={h}")
        else:
            print("ℹ️ No faces detected (expected for simple drawing)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during detection: {e}")
        return False

def download_haar_cascade():
    """Download Haar cascade file if not available"""
    print("\n📥 Downloading Haar Cascade File")
    print("=" * 50)
    
    try:
        import urllib.request
        
        cascade_url = "https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_frontalface_default.xml"
        cascade_filename = "haarcascade_frontalface_default.xml"
        
        if not os.path.exists(cascade_filename):
            print(f"Downloading from: {cascade_url}")
            urllib.request.urlretrieve(cascade_url, cascade_filename)
            print(f"✅ Downloaded to: {cascade_filename}")
        else:
            print(f"✅ Cascade file already exists: {cascade_filename}")
        
        # Test the downloaded file
        cascade = cv2.CascadeClassifier(cascade_filename)
        if cascade.empty():
            print("❌ Downloaded cascade is empty")
            return False
        else:
            print("✅ Downloaded cascade is working")
            return True
            
    except Exception as e:
        print(f"❌ Error downloading cascade: {e}")
        return False

def main():
    """Main debug function"""
    print("🚀 OpenCV Face Detection Debug")
    print("=" * 50)
    
    # Test basic detection
    if not test_simple_detection():
        print("\n⚠️ Haar cascade not working, trying to download...")
        if download_haar_cascade():
            print("\n🔄 Retesting with downloaded cascade...")
            test_simple_detection()
    
    print("\n" + "=" * 50)
    print("🎯 Debug Complete!")
    
    # Provide recommendations
    print("\n💡 Recommendations:")
    print("1. If cascade is working, the backend should work")
    print("2. If cascade failed, try reinstalling OpenCV:")
    print("   pip uninstall opencv-python")
    print("   pip install opencv-python")
    print("3. Alternative: pip install opencv-contrib-python")

if __name__ == "__main__":
    main()
