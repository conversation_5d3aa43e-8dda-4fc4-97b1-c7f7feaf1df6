import { useState, useEffect } from 'react'
import ImageUpload from './ImageUpload'

const KnownFaces = () => {
  const [knownFaces, setKnownFaces] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(null)
  
  // Add face form state
  const [showAddForm, setShowAddForm] = useState(false)
  const [newFaceImage, setNewFaceImage] = useState(null)
  const [newFaceName, setNewFaceName] = useState('')
  const [newFaceMetadata, setNewFaceMetadata] = useState({
    age: '',
    description: ''
  })

  useEffect(() => {
    fetchKnownFaces()
  }, [])

  const fetchKnownFaces = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/known_faces')
      const data = await response.json()
      if (data.success) {
        setKnownFaces(data.known_faces)
      }
    } catch (err) {
      setError('Failed to fetch known faces')
    }
  }

  const addKnownFace = async () => {
    if (!newFaceImage || !newFaceName.trim()) {
      setError('Please provide both an image and a name')
      return
    }

    setLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch('http://localhost:5000/api/add_face', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: newFaceImage,
          name: newFaceName.trim(),
          metadata: {
            ...newFaceMetadata,
            age: newFaceMetadata.age ? parseInt(newFaceMetadata.age) : undefined
          }
        })
      })

      const data = await response.json()

      if (data.success) {
        setSuccess(`Successfully added face for ${newFaceName}`)
        setNewFaceImage(null)
        setNewFaceName('')
        setNewFaceMetadata({ age: '', description: '' })
        setShowAddForm(false)
        fetchKnownFaces()
      } else {
        setError(data.error || 'Failed to add face')
      }
    } catch (err) {
      setError('Failed to connect to the server')
    } finally {
      setLoading(false)
    }
  }

  const removeKnownFace = async (name) => {
    if (!confirm(`Are you sure you want to remove ${name} from the database?`)) {
      return
    }

    setLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch('http://localhost:5000/api/remove_face', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name })
      })

      const data = await response.json()

      if (data.success) {
        setSuccess(`Successfully removed ${name}`)
        fetchKnownFaces()
      } else {
        setError(data.error || 'Failed to remove face')
      }
    } catch (err) {
      setError('Failed to connect to the server')
    } finally {
      setLoading(false)
    }
  }

  const handleImageSelect = (imageData, file) => {
    setNewFaceImage(imageData)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="card">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold">Known Faces Database</h2>
            <p className="text-gray-600">
              Manage the faces that can be recognized by the system
            </p>
          </div>
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="btn btn-primary"
          >
            {showAddForm ? 'Cancel' : 'Add New Face'}
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="card bg-green-50 border-green-200">
          <div className="flex items-center">
            <span className="text-green-500 mr-2">✅</span>
            <p className="text-green-700">{success}</p>
          </div>
        </div>
      )}

      {error && (
        <div className="card bg-red-50 border-red-200">
          <div className="flex items-center">
            <span className="text-red-500 mr-2">❌</span>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Add Face Form */}
      {showAddForm && (
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">Add New Face</h3>
          
          <div className="space-y-4">
            <div>
              <label className="form-label">Face Image</label>
              <ImageUpload onImageSelect={handleImageSelect} />
              <p className="text-sm text-gray-600 mt-1">
                Upload a clear image with exactly one face
              </p>
            </div>

            <div>
              <label className="form-label">Name *</label>
              <input
                type="text"
                value={newFaceName}
                onChange={(e) => setNewFaceName(e.target.value)}
                placeholder="Enter person's name"
                className="form-input"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">Age (optional)</label>
                <input
                  type="number"
                  value={newFaceMetadata.age}
                  onChange={(e) => setNewFaceMetadata({
                    ...newFaceMetadata,
                    age: e.target.value
                  })}
                  placeholder="Age"
                  className="form-input"
                  min="1"
                  max="120"
                />
              </div>

              <div>
                <label className="form-label">Description (optional)</label>
                <input
                  type="text"
                  value={newFaceMetadata.description}
                  onChange={(e) => setNewFaceMetadata({
                    ...newFaceMetadata,
                    description: e.target.value
                  })}
                  placeholder="Brief description"
                  className="form-input"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <button
                onClick={addKnownFace}
                disabled={loading || !newFaceImage || !newFaceName.trim()}
                className="btn btn-success"
              >
                {loading ? (
                  <>
                    <div className="spinner mr-2"></div>
                    Adding Face...
                  </>
                ) : (
                  'Add Face'
                )}
              </button>
              <button
                onClick={() => {
                  setShowAddForm(false)
                  setNewFaceImage(null)
                  setNewFaceName('')
                  setNewFaceMetadata({ age: '', description: '' })
                }}
                className="btn btn-secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Known Faces List */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4">
          Current Database ({knownFaces.length} faces)
        </h3>

        {knownFaces.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">👤</div>
            <p>No known faces in the database</p>
            <p className="text-sm">Add faces to enable recognition</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {knownFaces.map((face, index) => (
              <div key={index} className="border rounded-lg p-4 hover:shadow-md transition">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="font-medium text-lg">{face.name}</h4>
                    <p className="text-sm text-gray-600">
                      {face.count} encoding{face.count !== 1 ? 's' : ''}
                    </p>
                  </div>
                  <button
                    onClick={() => removeKnownFace(face.name)}
                    disabled={loading}
                    className="text-red-500 hover:text-red-700 text-sm"
                    title="Remove face"
                  >
                    🗑️
                  </button>
                </div>

                {face.metadata && (
                  <div className="space-y-1 text-sm text-gray-600">
                    {face.metadata.age && (
                      <div>Age: {face.metadata.age}</div>
                    )}
                    {face.metadata.description && (
                      <div>Description: {face.metadata.description}</div>
                    )}
                    {face.metadata.added_date && (
                      <div>
                        Added: {new Date(face.metadata.added_date).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="card bg-blue-50 border-blue-200">
        <h3 className="font-medium text-blue-800 mb-2">💡 Tips for Best Results</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Use clear, well-lit images with the face clearly visible</li>
          <li>• Ensure the image contains exactly one face</li>
          <li>• Front-facing photos work best</li>
          <li>• Add multiple photos of the same person for better recognition</li>
          <li>• Use consistent naming (e.g., "John Smith" not "john" or "John")</li>
        </ul>
      </div>
    </div>
  )
}

export default KnownFaces
