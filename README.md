# AI Face Detection & Recognition Application

A comprehensive web application for face detection and recognition using advanced computer vision techniques. Built with React frontend and Python Flask backend, featuring multiple detection algorithms and an attractive, responsive UI.

## Features

### 🔍 Face Detection
- **Multiple Detection Methods**:
  - Haar Cascades (Fast, traditional CV method)
  - MTCNN (Multi-task CNN, high accuracy)
  - Dlib HOG (Balanced speed and accuracy)
- Real-time performance comparison
- Confidence scores and bounding boxes
- Detailed detection statistics

### 👤 Face Recognition
- Add and manage known faces database
- Face encoding using state-of-the-art algorithms
- Adjustable recognition tolerance
- Metadata support (age, description, etc.)
- Recognition confidence scores

### 📹 Live Camera Integration
- Real-time webcam capture
- Live face detection/recognition
- Photo capture and processing
- Multiple camera resolution support

### 🎨 Modern UI/UX
- Responsive design with Tailwind CSS styling
- Drag-and-drop image upload
- Tabbed interface for different features
- Real-time feedback and loading states
- Mobile-friendly design

## Technology Stack

### Backend
- **Python 3.8+**
- **Flask** - Web framework
- **OpenCV** - Computer vision library
- **face_recognition** - Face recognition library (dlib-based)
- **MTCNN** - Deep learning face detection
- **NumPy** - Numerical computing
- **Pillow** - Image processing

### Frontend
- **React 18** - UI framework
- **Vite** - Build tool
- **Custom CSS** - Tailwind-inspired styling
- **Axios** - HTTP client

## Installation & Setup

### Prerequisites
- Python 3.8 or higher
- Node.js 16 or higher
- npm or yarn
- Webcam (optional, for live capture)

### Backend Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd face_detection
```

2. **Create virtual environment**
```bash
cd backend
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

3. **Install Python dependencies**
```bash
pip install -r requirements.txt
```

**Note**: Installing dlib and face_recognition can be challenging. If you encounter issues:

- **Windows**: Install Visual Studio Build Tools or use conda
- **macOS**: Install cmake: `brew install cmake`
- **Linux**: Install build essentials: `sudo apt-get install build-essential cmake`

Alternative installation with conda:
```bash
conda install -c conda-forge dlib
pip install face_recognition
```

4. **Run the backend server**
```bash
python app.py
```

The backend will start on `http://localhost:5000`

### Frontend Setup

1. **Navigate to frontend directory**
```bash
cd frontend
```

2. **Install dependencies**
```bash
npm install
```

3. **Start development server**
```bash
npm run dev
```

The frontend will start on `http://localhost:5173`

## Usage Guide

### 1. Face Detection
- Navigate to the "Face Detection" tab
- Choose your preferred detection method:
  - **Haar Cascades**: Fastest, good for real-time
  - **MTCNN**: Most accurate, slower processing
  - **Dlib HOG**: Balanced performance
- Upload an image or drag & drop
- Click "Detect Faces" to process
- View results with bounding boxes and statistics

### 2. Face Recognition
- First, add known faces in the "Manage Faces" tab
- Upload clear, front-facing photos with single faces
- Provide names and optional metadata
- Navigate to "Face Recognition" tab
- Adjust tolerance (0.3 = strict, 1.0 = lenient)
- Upload test images to recognize faces
- View recognition results with confidence scores

### 3. Live Camera
- Navigate to "Live Camera" tab
- Grant camera permissions when prompted
- Choose detection or recognition mode
- Select detection method for optimal performance
- Click "Start Camera" to begin live feed
- Capture photos and process them instantly

### 4. Managing Known Faces
- Use "Manage Faces" tab to build your database
- Add multiple photos of the same person for better accuracy
- Include metadata like age and description
- Remove faces when no longer needed
- View database statistics

## API Endpoints

### Face Detection
```
POST /api/detect
{
  "image": "base64_image_data",
  "method": "mtcnn|haar|dlib"
}
```

### Face Recognition
```
POST /api/recognize
{
  "image": "base64_image_data",
  "tolerance": 0.6
}
```

### Add Known Face
```
POST /api/add_face
{
  "image": "base64_image_data",
  "name": "Person Name",
  "metadata": {"age": 25, "description": "..."}
}
```

### Remove Known Face
```
DELETE /api/remove_face
{
  "name": "Person Name"
}
```

### Get Known Faces
```
GET /api/known_faces
```

## Performance Tips

1. **For Real-time Processing**: Use Haar Cascades
2. **For Accuracy**: Use MTCNN or Dlib
3. **Image Quality**: Use well-lit, clear images
4. **Face Size**: Ensure faces are at least 50x50 pixels
5. **Recognition**: Add multiple photos per person
6. **Tolerance**: Start with 0.6, adjust based on results

## Troubleshooting

### Common Issues

1. **Backend won't start**
   - Check Python version (3.8+)
   - Ensure all dependencies are installed
   - Try installing dlib with conda

2. **Camera not working**
   - Grant browser camera permissions
   - Check if camera is being used by other apps
   - Try different browsers (Chrome recommended)

3. **Poor recognition accuracy**
   - Add more photos of each person
   - Use clear, front-facing images
   - Adjust tolerance settings
   - Ensure good lighting in photos

4. **Slow processing**
   - Use Haar cascades for speed
   - Reduce image size before processing
   - Close other applications

### Error Messages

- **"No face found"**: Image doesn't contain detectable faces
- **"Multiple faces found"**: Use images with single faces for training
- **"Server connection failed"**: Ensure backend is running on port 5000

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- OpenCV community for computer vision tools
- dlib library for face recognition algorithms
- MTCNN implementation for deep learning face detection
- React and Vite teams for excellent development tools

## Support

For issues and questions:
1. Check the troubleshooting section
2. Search existing GitHub issues
3. Create a new issue with detailed description
4. Include error messages and system information
