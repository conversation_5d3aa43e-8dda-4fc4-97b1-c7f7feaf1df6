#!/usr/bin/env python3
"""
Browser demonstration - Test face detection and show results
"""

import cv2
import numpy as np
import base64
import requests
import json
import webbrowser
import time

def create_test_face_for_browser():
    """Create a test image that might be detected as a face"""
    print("🎨 Creating test image for browser demo...")
    
    # Create a more realistic face pattern
    img = np.ones((400, 400, 3), dtype=np.uint8) * 240  # Light gray background
    
    # Create a face-like pattern using geometric shapes
    # This creates a pattern that has face-like characteristics
    
    # Face oval (skin tone)
    cv2.ellipse(img, (200, 200), (80, 100), 0, 0, 360, (220, 180, 160), -1)
    
    # Hair area
    cv2.ellipse(img, (200, 150), (85, 60), 0, 0, 180, (101, 67, 33), -1)
    
    # Eye regions (darker areas where eyes would be)
    cv2.ellipse(img, (175, 180), (15, 10), 0, 0, 360, (180, 140, 120), -1)
    cv2.ellipse(img, (225, 180), (15, 10), 0, 0, 360, (180, 140, 120), -1)
    
    # Nose area
    cv2.ellipse(img, (200, 200), (8, 15), 0, 0, 360, (200, 160, 140), -1)
    
    # Mouth area
    cv2.ellipse(img, (200, 230), (20, 8), 0, 0, 180, (180, 120, 100), -1)
    
    # Add some facial structure
    cv2.ellipse(img, (200, 250), (60, 40), 0, 0, 180, (210, 170, 150), -1)
    
    filename = 'browser_test_face.jpg'
    cv2.imwrite(filename, img)
    print(f"✅ Test image saved as '{filename}'")
    
    return filename

def test_browser_api():
    """Test the API that the browser will use"""
    print("\n🔍 Testing browser API connection...")
    
    # Create test image
    test_image = create_test_face_for_browser()
    
    # Convert to base64 (same as browser will do)
    with open(test_image, 'rb') as f:
        image_data = f.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        image_data_url = f"data:image/jpeg;base64,{image_base64}"
    
    # Test the exact same API call the browser makes
    try:
        payload = {
            'image': image_data_url,
            'method': 'haar'
        }
        
        print("📡 Sending API request (same as browser)...")
        response = requests.post(
            'http://localhost:5000/api/detect',
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=15
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API Response:")
            print(f"   - Success: {data['success']}")
            print(f"   - Faces detected: {data['face_count']}")
            print(f"   - Method used: {data['method_used']}")
            print(f"   - Has result image: {'result_image' in data}")
            
            if data['face_count'] > 0:
                print("   - Detection details:")
                for i, detection in enumerate(data.get('detections', [])):
                    bbox = detection['bbox']
                    confidence = detection['confidence']
                    print(f"     Face {i+1}: bbox={bbox}, confidence={confidence:.2f}")
            
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Connection Error: {e}")
        return False

def show_browser_instructions():
    """Show instructions for using the browser interface"""
    print("\n" + "="*60)
    print("🌐 BROWSER INTERFACE INSTRUCTIONS")
    print("="*60)
    
    print("\n📋 How to Use the Face Detection App:")
    print("\n1. 🖼️  UPLOAD AN IMAGE:")
    print("   • Click the upload area or drag & drop an image")
    print("   • Use photos with clear human faces")
    print("   • Supported formats: JPG, PNG, GIF, BMP")
    
    print("\n2. ⚙️  SELECT DETECTION METHOD:")
    print("   • Haar Cascades: Fast, good for real-time")
    print("   • MTCNN: Most accurate (currently uses Haar)")
    print("   • Dlib: Balanced performance (currently uses Haar)")
    
    print("\n3. 🔍 DETECT FACES:")
    print("   • Click 'Detect Faces' button")
    print("   • Wait for processing (usually 1-3 seconds)")
    print("   • View results with bounding boxes")
    
    print("\n4. 📊 VIEW RESULTS:")
    print("   • See detected face count")
    print("   • Check processing time")
    print("   • View bounding boxes on image")
    
    print("\n🧪 QUICK TEST OPTIONS:")
    print("   • 'Load Sample Face Image' - Test with generated image")
    print("   • 'Test with Webcam' - Use your camera (requires permissions)")
    
    print("\n💡 TIPS FOR BEST RESULTS:")
    print("   • Use clear, well-lit photos")
    print("   • Front-facing faces work better")
    print("   • Ensure faces are at least 50x50 pixels")
    print("   • Try photos with multiple people")
    
    print("\n🔧 TROUBLESHOOTING:")
    print("   • If no faces detected: Try different photos")
    print("   • If API error: Check that backend is running")
    print("   • If slow: Use smaller images or Haar method")

def main():
    """Main browser demo function"""
    print("🚀 Face Detection App - Browser Demo")
    print("="*60)
    
    # Test API connectivity
    if test_browser_api():
        print("\n✅ Backend API is working perfectly!")
        print("✅ Browser interface is ready to use!")
    else:
        print("\n❌ API test failed - check backend server")
        return
    
    # Show instructions
    show_browser_instructions()
    
    print("\n" + "="*60)
    print("🎯 YOUR FACE DETECTION APP IS READY!")
    print("="*60)
    print("\n🌐 The browser interface is now open and ready to use.")
    print("📱 Try uploading a photo with faces to see the detection in action!")
    print("\n🔗 Backend API: http://localhost:5000")
    print("🔗 Frontend: file:///d:/codesoft/face_detection/demo.html")
    
    print("\n🎉 Enjoy testing your AI Face Detection Application!")

if __name__ == "__main__":
    main()
