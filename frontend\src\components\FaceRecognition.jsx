import { useState, useEffect } from 'react'
import ImageUpload from './ImageUpload'

const FaceRecognition = () => {
  const [selectedImage, setSelectedImage] = useState(null)
  const [tolerance, setTolerance] = useState(0.6)
  const [results, setResults] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [knownFaces, setKnownFaces] = useState([])

  useEffect(() => {
    fetchKnownFaces()
  }, [])

  const fetchKnownFaces = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/known_faces')
      const data = await response.json()
      if (data.success) {
        setKnownFaces(data.known_faces)
      }
    } catch (err) {
      console.error('Failed to fetch known faces:', err)
    }
  }

  const handleImageSelect = (imageData, file) => {
    setSelectedImage(imageData)
    setResults(null)
    setError(null)
  }

  const recognizeFaces = async () => {
    if (!selectedImage) {
      setError('Please select an image first')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('http://localhost:5000/api/recognize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: selectedImage,
          tolerance: tolerance
        })
      })

      const data = await response.json()

      if (data.success) {
        setResults(data)
      } else {
        setError(data.error || 'Recognition failed')
      }
    } catch (err) {
      setError('Failed to connect to the server. Make sure the backend is running.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Settings */}
      <div className="card">
        <h2 className="text-xl font-semibold mb-4">Recognition Settings</h2>
        
        <div className="space-y-4">
          <div>
            <label className="form-label">
              Recognition Tolerance: {tolerance.toFixed(2)}
            </label>
            <input
              type="range"
              min="0.3"
              max="1.0"
              step="0.05"
              value={tolerance}
              onChange={(e) => setTolerance(parseFloat(e.target.value))}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Strict (0.3)</span>
              <span>Balanced (0.6)</span>
              <span>Lenient (1.0)</span>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Lower values are more strict (fewer false positives), 
              higher values are more lenient (fewer false negatives).
            </p>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">Known Faces Database</h3>
            <p className="text-sm text-blue-700">
              {knownFaces.length > 0 
                ? `${knownFaces.length} known faces in database: ${knownFaces.map(f => f.name).join(', ')}`
                : 'No known faces in database. Add faces in the "Manage Faces" tab.'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Image Upload */}
      <div className="card">
        <h2 className="text-xl font-semibold mb-4">Upload Image for Recognition</h2>
        <ImageUpload onImageSelect={handleImageSelect} />
        
        {selectedImage && (
          <div className="mt-4 text-center">
            <button
              onClick={recognizeFaces}
              disabled={loading || knownFaces.length === 0}
              className="btn btn-primary"
            >
              {loading ? (
                <>
                  <div className="spinner mr-2"></div>
                  Recognizing Faces...
                </>
              ) : (
                'Recognize Faces'
              )}
            </button>
            {knownFaces.length === 0 && (
              <p className="text-sm text-red-600 mt-2">
                Add known faces first to enable recognition
              </p>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="card bg-red-50 border-red-200">
          <div className="flex items-center">
            <span className="text-red-500 mr-2">❌</span>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Results */}
      {results && (
        <div className="card">
          <h2 className="text-xl font-semibold mb-4">Recognition Results</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Result Image */}
            <div>
              <h3 className="font-medium mb-2">Processed Image</h3>
              <img
                src={results.result_image}
                alt="Recognition Result"
                className="w-full rounded-lg shadow"
              />
            </div>

            {/* Recognition Details */}
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Statistics</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Faces Found:</span>
                    <span className="font-semibold text-blue-600">
                      {results.face_count}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recognized:</span>
                    <span className="font-semibold text-green-600">
                      {results.recognitions?.filter(r => r.name !== 'Unknown').length || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Unknown:</span>
                    <span className="font-semibold text-red-600">
                      {results.recognitions?.filter(r => r.name === 'Unknown').length || 0}
                    </span>
                  </div>
                </div>
              </div>

              {/* Face Recognition Details */}
              {results.recognitions && results.recognitions.length > 0 && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2">Recognition Details</h3>
                  <div className="space-y-3">
                    {results.recognitions.map((recognition, index) => (
                      <div key={index} className="border-l-4 border-blue-500 pl-3">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">
                            {recognition.name === 'Unknown' ? '❓ Unknown Person' : `👤 ${recognition.name}`}
                          </span>
                          {recognition.confidence > 0 && (
                            <span className={`text-sm px-2 py-1 rounded ${
                              recognition.confidence > 0.7 
                                ? 'bg-green-100 text-green-800' 
                                : recognition.confidence > 0.5
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {(recognition.confidence * 100).toFixed(1)}%
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">
                          Position: ({recognition.bbox[0]}, {recognition.bbox[1]}) - 
                          ({recognition.bbox[2]}, {recognition.bbox[3]})
                        </div>
                        {recognition.metadata && Object.keys(recognition.metadata).length > 0 && (
                          <div className="text-xs text-gray-500 mt-1">
                            {Object.entries(recognition.metadata).map(([key, value]) => (
                              <div key={key}>{key}: {value}</div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FaceRecognition
