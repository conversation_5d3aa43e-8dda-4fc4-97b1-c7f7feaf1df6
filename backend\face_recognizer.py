import numpy as np
import cv2
import pickle
import os
from typing import List, Dict, Any, Tu<PERSON>, Optional
from datetime import datetime

class FaceRecognizer:
    def __init__(self, encodings_file: str = "known_faces.pkl"):
        self.encodings_file = encodings_file
        self.known_face_encodings = []
        self.known_face_names = []
        self.known_face_metadata = {}
        # Initialize Haar cascade classifier
        import os
        try:
            if hasattr(cv2, 'data') and hasattr(cv2.data, 'haarcascades'):
                cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
                self.haar_cascade = cv2.CascadeClassifier(cascade_path)
                if self.haar_cascade.empty():
                    raise Exception("Failed to load cascade")
            else:
                raise Exception("cv2.data not available")
        except:
            # Fallback
            opencv_path = os.path.dirname(cv2.__file__)
            cascade_path = os.path.join(opencv_path, 'data', 'haarcascade_frontalface_default.xml')
            if os.path.exists(cascade_path):
                self.haar_cascade = cv2.CascadeClassifier(cascade_path)
            else:
                self.haar_cascade = None
        self.load_known_faces()

    def load_known_faces(self):
        """Load known face encodings from file"""
        if os.path.exists(self.encodings_file):
            try:
                with open(self.encodings_file, 'rb') as f:
                    data = pickle.load(f)
                    self.known_face_encodings = data.get('encodings', [])
                    self.known_face_names = data.get('names', [])
                    self.known_face_metadata = data.get('metadata', {})
                print(f"Loaded {len(self.known_face_encodings)} known faces")
            except Exception as e:
                print(f"Error loading known faces: {e}")
                self.known_face_encodings = []
                self.known_face_names = []
                self.known_face_metadata = {}

    def save_known_faces(self):
        """Save known face encodings to file"""
        try:
            data = {
                'encodings': self.known_face_encodings,
                'names': self.known_face_names,
                'metadata': self.known_face_metadata
            }
            with open(self.encodings_file, 'wb') as f:
                pickle.dump(data, f)
            print(f"Saved {len(self.known_face_encodings)} known faces")
        except Exception as e:
            print(f"Error saving known faces: {e}")

    def add_known_face(self, image: np.ndarray, name: str, metadata: Dict[str, Any] = None) -> bool:
        """
        Add a new known face to the database

        Args:
            image: Face image as numpy array
            name: Name of the person
            metadata: Additional metadata (age, description, etc.)

        Returns:
            True if face was successfully added, False otherwise
        """
        try:
            # Convert to grayscale for face detection
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Detect faces
            if self.haar_cascade is None:
                print("Haar cascade not available")
                return False
            faces = self.haar_cascade.detectMultiScale(gray, 1.1, 5)

            if len(faces) == 0:
                print("No face found in the image")
                return False

            if len(faces) > 1:
                print("Multiple faces found, using the largest one")

            # Use the largest face
            largest_face = max(faces, key=lambda f: f[2] * f[3])
            x, y, w, h = largest_face

            # Extract face region and resize to standard size
            face_roi = gray[y:y+h, x:x+w]
            face_roi = cv2.resize(face_roi, (100, 100))

            # Store the face template
            self.known_face_encodings.append(face_roi.flatten())
            self.known_face_names.append(name)

            # Add metadata
            if metadata is None:
                metadata = {}
            metadata['added_date'] = datetime.now().isoformat()
            self.known_face_metadata[name] = metadata

            # Save to file
            self.save_known_faces()

            print(f"Added face for {name}")
            return True

        except Exception as e:
            print(f"Error adding known face: {e}")
            return False

    def remove_known_face(self, name: str) -> bool:
        """Remove a known face from the database"""
        try:
            if name in self.known_face_names:
                # Find all indices with this name
                indices_to_remove = [i for i, n in enumerate(self.known_face_names) if n == name]

                # Remove in reverse order to maintain indices
                for i in reversed(indices_to_remove):
                    del self.known_face_encodings[i]
                    del self.known_face_names[i]

                # Remove metadata
                if name in self.known_face_metadata:
                    del self.known_face_metadata[name]

                self.save_known_faces()
                print(f"Removed face for {name}")
                return True
            else:
                print(f"Face for {name} not found")
                return False

        except Exception as e:
            print(f"Error removing known face: {e}")
            return False

    def recognize_faces(self, image: np.ndarray, tolerance: float = 0.6) -> List[Dict[str, Any]]:
        """
        Recognize faces in an image

        Args:
            image: Input image as numpy array
            tolerance: Face recognition tolerance (lower = more strict)

        Returns:
            List of recognition results
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Detect faces
            if self.haar_cascade is None:
                print("Haar cascade not available")
                return []
            faces = self.haar_cascade.detectMultiScale(gray, 1.1, 5)

            results = []

            for (x, y, w, h) in faces:
                # Extract face region and resize to standard size
                face_roi = gray[y:y+h, x:x+w]
                face_roi = cv2.resize(face_roi, (100, 100))

                name = "Unknown"
                confidence = 0.0
                metadata = {}

                # Compare with known faces using template matching
                if len(self.known_face_encodings) > 0:
                    similarities = []
                    for known_encoding in self.known_face_encodings:
                        # Calculate normalized correlation
                        correlation = cv2.matchTemplate(
                            face_roi,
                            np.array(known_encoding).reshape(100, 100).astype(np.uint8),
                            cv2.TM_CCOEFF_NORMED
                        )[0][0]
                        similarities.append(correlation)

                    best_match_index = np.argmax(similarities)
                    best_similarity = similarities[best_match_index]

                    # Use tolerance to determine if it's a match
                    if best_similarity > (1 - tolerance):
                        name = self.known_face_names[best_match_index]
                        confidence = best_similarity
                        metadata = self.known_face_metadata.get(name, {})

                results.append({
                    'bbox': [x, y, x + w, y + h],
                    'name': name,
                    'confidence': confidence,
                    'metadata': metadata
                })

            return results

        except Exception as e:
            print(f"Error recognizing faces: {e}")
            return []

    def get_known_faces_list(self) -> List[Dict[str, Any]]:
        """Get list of all known faces with metadata"""
        faces_list = []
        unique_names = list(set(self.known_face_names))

        for name in unique_names:
            count = self.known_face_names.count(name)
            metadata = self.known_face_metadata.get(name, {})

            faces_list.append({
                'name': name,
                'count': count,
                'metadata': metadata
            })

        return faces_list

    def draw_recognition_results(self, image: np.ndarray, results: List[Dict[str, Any]]) -> np.ndarray:
        """Draw recognition results on image"""
        result_image = image.copy()

        for result in results:
            bbox = result['bbox']
            name = result['name']
            confidence = result['confidence']

            x1, y1, x2, y2 = bbox

            # Choose color based on recognition
            color = (0, 255, 0) if name != "Unknown" else (0, 0, 255)

            # Draw bounding box
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)

            # Draw name and confidence
            label = f"{name}"
            if confidence > 0:
                label += f" ({confidence:.2f})"

            # Calculate text size for background
            (text_width, text_height), _ = cv2.getTextSize(
                label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2
            )

            # Draw background rectangle for text
            cv2.rectangle(result_image, (x1, y1 - text_height - 10),
                         (x1 + text_width, y1), color, -1)

            # Draw text
            cv2.putText(result_image, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return result_image

    def process_image_with_recognition(self, image: np.ndarray, tolerance: float = 0.6) -> Tuple[np.ndarray, List[Dict[str, Any]]]:
        """Process image with face recognition and return annotated image"""
        results = self.recognize_faces(image, tolerance)
        annotated_image = self.draw_recognition_results(image, results)
        return annotated_image, results
