<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Detection App - Status</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
        }
        .status-icon {
            font-size: 3rem;
            margin-bottom: 10px;
        }
        .status-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .status-desc {
            opacity: 0.8;
            font-size: 0.9rem;
        }
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 1rem;
        }
        .btn-primary {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
        }
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .features {
            margin-top: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
        }
        .features h3 {
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 5px 0;
        }
        .api-status {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .success { color: #4ade80; }
        .error { color: #f87171; }
        .warning { color: #fbbf24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🤖 AI Face Detection</h1>
            <p class="subtitle">Advanced Computer Vision Application</p>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <div class="status-icon">🖥️</div>
                <div class="status-title">Backend API</div>
                <div class="status-desc">Python Flask + OpenCV</div>
                <div id="backend-status" class="api-status">Checking...</div>
            </div>

            <div class="status-card">
                <div class="status-icon">🌐</div>
                <div class="status-title">Frontend Interface</div>
                <div class="status-desc">Modern Web Application</div>
                <div class="api-status success">✅ Ready</div>
            </div>

            <div class="status-card">
                <div class="status-icon">👁️</div>
                <div class="status-title">Face Detection</div>
                <div class="status-desc">OpenCV Haar Cascades</div>
                <div class="api-status success">✅ Operational</div>
            </div>

            <div class="status-card">
                <div class="status-icon">📊</div>
                <div class="status-title">Test Results</div>
                <div class="status-desc">API Connectivity</div>
                <div id="test-status" class="api-status">Testing...</div>
            </div>
        </div>

        <div class="features">
            <h3>🚀 Application Features</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <span>✅</span>
                    <span>Real-time face detection</span>
                </div>
                <div class="feature-item">
                    <span>✅</span>
                    <span>Multiple detection methods</span>
                </div>
                <div class="feature-item">
                    <span>✅</span>
                    <span>Drag & drop image upload</span>
                </div>
                <div class="feature-item">
                    <span>✅</span>
                    <span>Live camera integration</span>
                </div>
                <div class="feature-item">
                    <span>✅</span>
                    <span>Bounding box visualization</span>
                </div>
                <div class="feature-item">
                    <span>✅</span>
                    <span>Processing statistics</span>
                </div>
                <div class="feature-item">
                    <span>✅</span>
                    <span>Responsive design</span>
                </div>
                <div class="feature-item">
                    <span>✅</span>
                    <span>Cross-platform support</span>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="demo.html" class="btn btn-primary">🚀 Launch Face Detection App</a>
            <button onclick="testAPI()" class="btn btn-secondary">🧪 Test API</button>
            <button onclick="checkStatus()" class="btn btn-secondary">🔄 Refresh Status</button>
        </div>

        <div class="features">
            <h3>📋 Quick Start Guide</h3>
            <ol style="line-height: 1.8;">
                <li><strong>Click "Launch Face Detection App"</strong> to open the main interface</li>
                <li><strong>Upload an image</strong> with human faces (drag & drop or click to select)</li>
                <li><strong>Select detection method</strong> (Haar Cascades recommended for speed)</li>
                <li><strong>Click "Detect Faces"</strong> to process the image</li>
                <li><strong>View results</strong> with bounding boxes and statistics</li>
            </ol>
        </div>
    </div>

    <script>
        async function checkBackendStatus() {
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    document.getElementById('backend-status').innerHTML = 
                        '<span class="success">✅ ' + data.message + '</span>';
                } else {
                    document.getElementById('backend-status').innerHTML = 
                        '<span class="warning">⚠️ Unexpected response</span>';
                }
            } catch (error) {
                document.getElementById('backend-status').innerHTML = 
                    '<span class="error">❌ Not responding</span>';
            }
        }

        async function testAPI() {
            document.getElementById('test-status').innerHTML = 'Testing...';
            
            try {
                // Create a simple test image (1x1 pixel)
                const canvas = document.createElement('canvas');
                canvas.width = 100;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#f0f0f0';
                ctx.fillRect(0, 0, 100, 100);
                
                const imageData = canvas.toDataURL('image/jpeg');
                
                const response = await fetch('http://localhost:5000/api/detect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image: imageData,
                        method: 'haar'
                    })
                });

                const data = await response.json();
                
                if (data.success !== undefined) {
                    document.getElementById('test-status').innerHTML = 
                        '<span class="success">✅ API Working</span>';
                } else {
                    document.getElementById('test-status').innerHTML = 
                        '<span class="warning">⚠️ Unexpected response</span>';
                }
            } catch (error) {
                document.getElementById('test-status').innerHTML = 
                    '<span class="error">❌ Test failed</span>';
            }
        }

        function checkStatus() {
            checkBackendStatus();
            testAPI();
        }

        // Initial status check
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
