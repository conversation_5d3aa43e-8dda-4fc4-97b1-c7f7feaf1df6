<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Face Detection & Recognition</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .header {
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            font-size: 2rem;
        }

        .title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
        }

        .subtitle {
            color: #6b7280;
            font-size: 0.875rem;
        }

        .nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 0;
        }

        .nav-tabs {
            display: flex;
            gap: 0.25rem;
        }

        .nav-tab {
            padding: 0.75rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .nav-tab.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
            background: #eff6ff;
        }

        .nav-tab:hover {
            color: #1f2937;
            background: #f9fafb;
        }

        .main {
            padding: 2rem 0;
        }

        .card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 0.5rem;
            padding: 3rem;
            text-align: center;
            transition: all 0.2s;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #9ca3af;
        }

        .upload-area.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .upload-icon {
            font-size: 3rem;
            color: #9ca3af;
            margin-bottom: 1rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
            font-size: 0.875rem;
        }

        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
        }

        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }

        .btn-danger {
            background-color: #ef4444;
            color: white;
        }

        .grid {
            display: grid;
            gap: 1rem;
        }

        .grid-cols-3 {
            grid-template-columns: repeat(3, 1fr);
        }

        .method-card {
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .method-card:hover {
            border-color: #9ca3af;
        }

        .method-card.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .method-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .method-desc {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.75rem;
        }

        .pros-cons {
            font-size: 0.75rem;
        }

        .pros {
            color: #059669;
            margin-bottom: 0.5rem;
        }

        .cons {
            color: #dc2626;
        }

        .hidden {
            display: none;
        }

        .result-image {
            max-width: 100%;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .stats {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-weight: 600;
            color: #3b82f6;
        }

        .footer {
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 2rem 0;
            text-align: center;
            color: #6b7280;
            margin-top: 3rem;
        }

        @media (max-width: 768px) {
            .grid-cols-3 {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                text-align: center;
            }
            
            .nav-tabs {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🤖</div>
                <div>
                    <h1 class="title">AI Face Detection & Recognition</h1>
                    <p class="subtitle">Advanced computer vision powered by deep learning</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('detection')">
                    🔍 Face Detection
                </button>
                <button class="nav-tab" onclick="showTab('recognition')">
                    👤 Face Recognition
                </button>
                <button class="nav-tab" onclick="showTab('webcam')">
                    📹 Live Camera
                </button>
                <button class="nav-tab" onclick="showTab('manage')">
                    ⚙️ Manage Faces
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Face Detection Tab -->
            <div id="detection-tab" class="tab-content">
                <div class="card">
                    <h2 class="card-title">Detection Method</h2>
                    <div class="grid grid-cols-3">
                        <div class="method-card selected" onclick="selectMethod('haar')">
                            <div class="method-title">Haar Cascades</div>
                            <div class="method-desc">Fast, traditional computer vision method</div>
                            <div class="pros-cons">
                                <div class="pros">✓ Very fast ✓ Low resource usage</div>
                                <div class="cons">✗ Less accurate ✗ Sensitive to lighting</div>
                            </div>
                        </div>
                        <div class="method-card" onclick="selectMethod('mtcnn')">
                            <div class="method-title">MTCNN</div>
                            <div class="method-desc">Multi-task CNN for face detection</div>
                            <div class="pros-cons">
                                <div class="pros">✓ High accuracy ✓ Facial landmarks</div>
                                <div class="cons">✗ Slower processing ✗ Higher resource usage</div>
                            </div>
                        </div>
                        <div class="method-card" onclick="selectMethod('dlib')">
                            <div class="method-title">Dlib HOG</div>
                            <div class="method-desc">Histogram of Oriented Gradients</div>
                            <div class="pros-cons">
                                <div class="pros">✓ Good accuracy ✓ Moderate speed</div>
                                <div class="cons">✗ Moderate resources ✗ Less robust</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2 class="card-title">Upload Image</h2>
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">📷</div>
                        <p><strong>Drop an image here, or click to select</strong></p>
                        <p style="font-size: 0.875rem; color: #6b7280;">Supports JPG, PNG, GIF, BMP formats</p>
                    </div>
                    <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
                    
                    <div id="imagePreview" class="hidden" style="margin-top: 1rem; text-align: center;">
                        <img id="previewImg" class="result-image" style="max-height: 300px;">
                        <div style="margin-top: 1rem;">
                            <button class="btn btn-primary" onclick="detectFaces()">Detect Faces</button>
                            <button class="btn btn-secondary" onclick="clearImage()">Clear</button>
                        </div>
                    </div>
                </div>

                <div id="detectionResults" class="card hidden">
                    <h2 class="card-title">Detection Results</h2>
                    <div class="stats">
                        <div class="stat-item">
                            <span>Faces Detected:</span>
                            <span class="stat-value" id="faceCount">0</span>
                        </div>
                        <div class="stat-item">
                            <span>Method Used:</span>
                            <span class="stat-value" id="methodUsed">HAAR</span>
                        </div>
                        <div class="stat-item">
                            <span>Processing Time:</span>
                            <span class="stat-value" id="processingTime">0ms</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other tabs (hidden by default) -->
            <div id="recognition-tab" class="tab-content hidden">
                <div class="card">
                    <h2 class="card-title">Face Recognition</h2>
                    <p>Face recognition functionality requires the backend server to be running with the face_recognition library installed.</p>
                    <p style="margin-top: 1rem; color: #6b7280;">This demo shows the UI design. The full functionality is available when you run the complete application.</p>
                </div>
            </div>

            <div id="webcam-tab" class="tab-content hidden">
                <div class="card">
                    <h2 class="card-title">Live Camera Capture</h2>
                    <p>Live camera functionality requires camera permissions and the backend server.</p>
                    <p style="margin-top: 1rem; color: #6b7280;">This demo shows the UI design. The full functionality is available when you run the complete application.</p>
                </div>
            </div>

            <div id="manage-tab" class="tab-content hidden">
                <div class="card">
                    <h2 class="card-title">Manage Known Faces</h2>
                    <p>Face management functionality requires the backend server to be running.</p>
                    <p style="margin-top: 1rem; color: #6b7280;">This demo shows the UI design. The full functionality is available when you run the complete application.</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>Built with React, OpenCV, MTCNN, and face_recognition library</p>
            <p style="font-size: 0.875rem; margin-top: 0.5rem;">
                Supports Haar Cascades, MTCNN, and dlib face detection methods
            </p>
        </div>
    </footer>

    <script>
        let selectedMethod = 'haar';
        let selectedImage = null;

        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            
            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }

        function selectMethod(method) {
            selectedMethod = method;
            
            // Remove selected class from all method cards
            document.querySelectorAll('.method-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            event.target.classList.add('selected');
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    selectedImage = e.target.result;
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').classList.remove('hidden');
                    document.getElementById('detectionResults').classList.add('hidden');
                };
                reader.readAsDataURL(file);
            }
        }

        function clearImage() {
            selectedImage = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('imagePreview').classList.add('hidden');
            document.getElementById('detectionResults').classList.add('hidden');
        }

        async function detectFaces() {
            if (!selectedImage) {
                alert('Please select an image first');
                return;
            }

            const startTime = Date.now();

            // Show loading state
            const detectBtn = document.querySelector('.btn-primary');
            const originalText = detectBtn.textContent;
            detectBtn.textContent = 'Processing...';
            detectBtn.disabled = true;

            try {
                console.log('Sending request to backend...');
                const response = await fetch('http://localhost:5000/api/detect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image: selectedImage,
                        method: selectedMethod
                    })
                });

                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);

                const processingTime = Date.now() - startTime;

                if (data.success) {
                    // Update results
                    document.getElementById('faceCount').textContent = data.face_count || 0;
                    document.getElementById('methodUsed').textContent = (data.method_used || selectedMethod).toUpperCase();
                    document.getElementById('processingTime').textContent = processingTime + 'ms';

                    // Show processed image if available
                    if (data.result_image) {
                        document.getElementById('previewImg').src = data.result_image;
                    }

                    // Show results
                    document.getElementById('detectionResults').classList.remove('hidden');

                    // Show success message
                    if (data.face_count > 0) {
                        alert(`Successfully detected ${data.face_count} face(s)!`);
                    } else {
                        alert('No faces detected in the image.');
                    }
                } else {
                    alert('Detection failed: ' + (data.error || 'Unknown error'));
                    console.error('Detection error:', data.error);
                }
            } catch (error) {
                alert('Failed to connect to the server. Make sure the backend is running on http://localhost:5000');
                console.error('Network error:', error);
            } finally {
                // Restore button state
                detectBtn.textContent = originalText;
                detectBtn.disabled = false;
            }
        }

        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                const event = { target: { files: [files[0]] } };
                handleFileSelect(event);
            }
        });
    </script>
</body>
</html>
