import { useState } from 'react'
import ImageUpload from './ImageUpload'

const FaceDetection = () => {
  const [selectedImage, setSelectedImage] = useState(null)
  const [detectionMethod, setDetectionMethod] = useState('mtcnn')
  const [results, setResults] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  const detectionMethods = [
    {
      id: 'haar',
      name: 'Haar Cascades',
      description: 'Fast, traditional computer vision method',
      pros: ['Very fast', 'Low resource usage', 'Good for real-time'],
      cons: ['Less accurate', 'Sensitive to lighting']
    },
    {
      id: 'mtcnn',
      name: 'MTCNN',
      description: 'Multi-task CNN for face detection',
      pros: ['High accuracy', 'Detects facial landmarks', 'Robust to variations'],
      cons: ['Slower processing', 'Higher resource usage']
    },
    {
      id: 'dlib',
      name: 'Dlib HOG',
      description: 'Histogram of Oriented Gradients',
      pros: ['Good accuracy', 'Moderate speed', 'Reliable'],
      cons: ['Moderate resource usage', 'Less robust than MTCNN']
    }
  ]

  const handleImageSelect = (imageData, file) => {
    setSelectedImage(imageData)
    setResults(null)
    setError(null)
  }

  const detectFaces = async () => {
    if (!selectedImage) {
      setError('Please select an image first')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('http://localhost:5000/api/detect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: selectedImage,
          method: detectionMethod
        })
      })

      const data = await response.json()

      if (data.success) {
        setResults(data)
      } else {
        setError(data.error || 'Detection failed')
      }
    } catch (err) {
      setError('Failed to connect to the server. Make sure the backend is running.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Method Selection */}
      <div className="card">
        <h2 className="text-xl font-semibold mb-4">Detection Method</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {detectionMethods.map((method) => (
            <div
              key={method.id}
              className={`p-4 border rounded-lg cursor-pointer transition ${
                detectionMethod === method.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setDetectionMethod(method.id)}
            >
              <div className="flex items-center mb-2">
                <input
                  type="radio"
                  checked={detectionMethod === method.id}
                  onChange={() => setDetectionMethod(method.id)}
                  className="mr-2"
                />
                <h3 className="font-medium">{method.name}</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">{method.description}</p>
              
              <div className="space-y-2">
                <div>
                  <p className="text-xs font-medium text-green-600">Pros:</p>
                  <ul className="text-xs text-gray-600">
                    {method.pros.map((pro, index) => (
                      <li key={index}>• {pro}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <p className="text-xs font-medium text-red-600">Cons:</p>
                  <ul className="text-xs text-gray-600">
                    {method.cons.map((con, index) => (
                      <li key={index}>• {con}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Image Upload */}
      <div className="card">
        <h2 className="text-xl font-semibold mb-4">Upload Image</h2>
        <ImageUpload onImageSelect={handleImageSelect} />
        
        {selectedImage && (
          <div className="mt-4 text-center">
            <button
              onClick={detectFaces}
              disabled={loading}
              className="btn btn-primary"
            >
              {loading ? (
                <>
                  <div className="spinner mr-2"></div>
                  Detecting Faces...
                </>
              ) : (
                'Detect Faces'
              )}
            </button>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="card bg-red-50 border-red-200">
          <div className="flex items-center">
            <span className="text-red-500 mr-2">❌</span>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Results */}
      {results && (
        <div className="card">
          <h2 className="text-xl font-semibold mb-4">Detection Results</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Result Image */}
            <div>
              <h3 className="font-medium mb-2">Processed Image</h3>
              <img
                src={results.result_image}
                alt="Detection Result"
                className="w-full rounded-lg shadow"
              />
            </div>

            {/* Statistics */}
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Statistics</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Faces Detected:</span>
                    <span className="font-semibold text-blue-600">
                      {results.face_count}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Method Used:</span>
                    <span className="font-semibold">
                      {results.method_used.toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Face Details */}
              {results.detections && results.detections.length > 0 && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2">Face Details</h3>
                  <div className="space-y-2">
                    {results.detections.map((detection, index) => (
                      <div key={index} className="text-sm">
                        <div className="font-medium">Face {index + 1}</div>
                        <div className="text-gray-600">
                          Confidence: {(detection.confidence * 100).toFixed(1)}%
                        </div>
                        <div className="text-gray-600">
                          Position: ({detection.bbox[0]}, {detection.bbox[1]}) - 
                          ({detection.bbox[2]}, {detection.bbox[3]})
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FaceDetection
