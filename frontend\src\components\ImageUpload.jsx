import { useState, useRef } from 'react'

const ImageUpload = ({ onImageSelect, accept = "image/*", className = "" }) => {
  const [dragActive, setDragActive] = useState(false)
  const [preview, setPreview] = useState(null)
  const fileInputRef = useRef(null)

  const handleDrag = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0])
    }
  }

  const handleChange = (e) => {
    e.preventDefault()
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0])
    }
  }

  const handleFile = (file) => {
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file')
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const imageData = e.target.result
      setPreview(imageData)
      if (onImageSelect) {
        onImageSelect(imageData, file)
      }
    }
    reader.readAsDataURL(file)
  }

  const onButtonClick = () => {
    fileInputRef.current?.click()
  }

  const clearImage = () => {
    setPreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    if (onImageSelect) {
      onImageSelect(null, null)
    }
  }

  return (
    <div className={`w-full ${className}`}>
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition ${
          dragActive
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          accept={accept}
          onChange={handleChange}
        />

        {preview ? (
          <div className="space-y-4">
            <img
              src={preview}
              alt="Preview"
              className="max-w-full max-h-64 mx-auto rounded-lg shadow"
            />
            <div className="flex gap-2 justify-center">
              <button
                onClick={onButtonClick}
                className="btn btn-secondary"
              >
                Change Image
              </button>
              <button
                onClick={clearImage}
                className="btn btn-danger"
              >
                Remove
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-6xl text-gray-400">📷</div>
            <div>
              <p className="text-lg font-medium text-gray-700">
                Drop an image here, or click to select
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Supports JPG, PNG, GIF, BMP formats
              </p>
            </div>
            <button
              onClick={onButtonClick}
              className="btn btn-primary"
            >
              Select Image
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default ImageUpload
