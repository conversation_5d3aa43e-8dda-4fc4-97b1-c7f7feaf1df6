from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import cv2
import numpy as np
import base64
import io
from PIL import Image
import os
import tempfile
from werkzeug.utils import secure_filename
import json

from face_detector import FaceDetector
from face_recognizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
def convert_numpy_types(data):
    """Convert NumPy types to Python native types for JSON serialization"""
    if isinstance(data, dict):
        return {key: convert_numpy_types(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif isinstance(data, np.bool_):
        return bool(data)
    else:
        return data


app = Flask(__name__)
CORS(app)

# Initialize face detection and recognition
face_detector = FaceDetector()
face_recognizer = FaceRecognizer()

# Configuration
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

# Create upload folder if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def decode_base64_image(base64_string):
    """Decode base64 image to numpy array"""
    try:
        # Remove data URL prefix if present
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]

        # Add padding if necessary
        missing_padding = len(base64_string) % 4
        if missing_padding:
            base64_string += '=' * (4 - missing_padding)

        image_data = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_data))
        return cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"Error decoding image: {e}")
        raise ValueError(f"Invalid image data: {e}")

def encode_image_to_base64(image):
    """Encode numpy array image to base64"""
    _, buffer = cv2.imencode('.jpg', image)
    image_base64 = base64.b64encode(buffer).decode('utf-8')
    return f"data:image/jpeg;base64,{image_base64}"

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'message': 'Face detection API is running'})

@app.route('/api/detect', methods=['POST'])
def detect_faces():
    """Detect faces in uploaded image"""
    try:
        print("Received face detection request")
        data = request.get_json()

        if not data or 'image' not in data:
            print("Error: No image data provided")
            return jsonify({'error': 'No image data provided'}), 400

        method = data.get('method', 'haar')  # Default to haar for reliability
        print(f"Using detection method: {method}")

        # Decode image
        print("Decoding base64 image...")
        image = decode_base64_image(data['image'])
        print(f"Image decoded successfully. Shape: {image.shape}")

        # Detect faces
        print("Starting face detection...")
        detections = face_detector.detect_faces(image, method)
        print(f"Face detection complete. Found {len(detections)} faces")

        # Draw faces on image
        print("Drawing bounding boxes...")
        result_image = face_detector.draw_faces(image, detections)

        # Encode result image
        print("Encoding result image...")
        result_base64 = encode_image_to_base64(result_image)

        print("Sending response...")
        response_data = {
            'success': True,
            'detections': detections,
            'result_image': result_base64,
            'face_count': len(detections),
            'method_used': method
        }
        
        # Convert NumPy types to Python types
        response_data = convert_numpy_types(response_data)
        
        return jsonify(response_data)

    except Exception as e:
        print(f"Error in face detection: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/recognize', methods=['POST'])
def recognize_faces():
    """Recognize faces in uploaded image"""
    try:
        data = request.get_json()

        if not data or 'image' not in data:
            return jsonify({'error': 'No image data provided'}), 400

        tolerance = float(data.get('tolerance', 0.6))

        # Decode image
        image = decode_base64_image(data['image'])

        # Recognize faces
        result_image, results = face_recognizer.process_image_with_recognition(image, tolerance)

        # Encode result image
        result_base64 = encode_image_to_base64(result_image)

        return jsonify({
            'success': True,
            'recognitions': results,
            'result_image': result_base64,
            'face_count': len(results)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/add_face', methods=['POST'])
def add_known_face():
    """Add a new known face to the database"""
    try:
        data = request.get_json()

        if not data or 'image' not in data or 'name' not in data:
            return jsonify({'error': 'Image and name are required'}), 400

        name = data['name'].strip()
        metadata = data.get('metadata', {})

        if not name:
            return jsonify({'error': 'Name cannot be empty'}), 400

        # Decode image
        image = decode_base64_image(data['image'])

        # Add face to database
        success = face_recognizer.add_known_face(image, name, metadata)

        if success:
            return jsonify({
                'success': True,
                'message': f'Face for {name} added successfully'
            })
        else:
            return jsonify({'error': 'Failed to add face. Make sure the image contains exactly one face.'}), 400

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/remove_face', methods=['DELETE'])
def remove_known_face():
    """Remove a known face from the database"""
    try:
        data = request.get_json()

        if not data or 'name' not in data:
            return jsonify({'error': 'Name is required'}), 400

        name = data['name'].strip()

        success = face_recognizer.remove_known_face(name)

        if success:
            return jsonify({
                'success': True,
                'message': f'Face for {name} removed successfully'
            })
        else:
            return jsonify({'error': f'Face for {name} not found'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/known_faces', methods=['GET'])
def get_known_faces():
    """Get list of all known faces"""
    try:
        faces_list = face_recognizer.get_known_faces_list()
        return jsonify({
            'success': True,
            'known_faces': faces_list,
            'total_count': len(faces_list)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Upload file endpoint"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']

        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not allowed'}), 400

        # Read and process image
        image_data = file.read()
        image = Image.open(io.BytesIO(image_data))
        image_array = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # Encode to base64
        image_base64 = encode_image_to_base64(image_array)

        return jsonify({
            'success': True,
            'image': image_base64,
            'filename': secure_filename(file.filename or 'uploaded_image')
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/methods', methods=['GET'])
def get_detection_methods():
    """Get available detection methods"""
    return jsonify({
        'methods': [
            {
                'id': 'haar',
                'name': 'Haar Cascades',
                'description': 'Fast, traditional computer vision method'
            },
            {
                'id': 'mtcnn',
                'name': 'MTCNN',
                'description': 'Deep learning-based, high accuracy'
            },
            {
                'id': 'dlib',
                'name': 'Dlib HOG',
                'description': 'Histogram of Oriented Gradients'
            }
        ]
    })

if __name__ == '__main__':
    print("Starting Face Detection API...")
    print("Available endpoints:")
    print("  GET  /api/health - Health check")
    print("  POST /api/detect - Detect faces")
    print("  POST /api/recognize - Recognize faces")
    print("  POST /api/add_face - Add known face")
    print("  DELETE /api/remove_face - Remove known face")
    print("  GET  /api/known_faces - List known faces")
    print("  POST /api/upload - Upload file")
    print("  GET  /api/methods - Get detection methods")

    app.run(debug=True, host='0.0.0.0', port=5000)
