#!/usr/bin/env python3
"""
Final demonstration of the Face Detection Application
Shows the complete working system
"""

import cv2
import numpy as np
import base64
import requests
import json
import webbrowser
import os
import time

def create_demo_face_image():
    """Create a more realistic face image for demonstration"""
    print("🎨 Creating demonstration face image...")
    
    # Create a larger, more detailed image
    img = np.ones((600, 500, 3), dtype=np.uint8) * 245  # Light background
    
    # Draw a realistic face structure
    center = (250, 300)
    
    # Face shape (oval)
    cv2.ellipse(img, center, (120, 150), 0, 0, 360, (220, 180, 160), -1)  # Face
    cv2.ellipse(img, center, (120, 150), 0, 0, 360, (200, 160, 140), 3)   # Face outline
    
    # Hair
    hair_points = np.array([
        [130, 200], [370, 200], [380, 180], [370, 160], 
        [350, 140], [320, 120], [280, 110], [250, 105], 
        [220, 110], [190, 120], [160, 140], [140, 160], 
        [130, 180], [120, 200]
    ], np.int32)
    cv2.fillPoly(img, [hair_points], (80, 50, 20))  # Dark brown hair
    
    # Forehead
    cv2.ellipse(img, (250, 220), (100, 40), 0, 0, 180, (210, 170, 150), -1)
    
    # Eyes with more detail
    # Left eye
    cv2.ellipse(img, (210, 260), (25, 15), 0, 0, 360, (255, 255, 255), -1)  # Eye white
    cv2.ellipse(img, (210, 260), (12, 12), 0, 0, 360, (139, 69, 19), -1)     # Iris
    cv2.ellipse(img, (210, 260), (6, 6), 0, 0, 360, (0, 0, 0), -1)           # Pupil
    cv2.ellipse(img, (210, 255), (3, 2), 0, 0, 360, (255, 255, 255), -1)     # Highlight
    
    # Right eye
    cv2.ellipse(img, (290, 260), (25, 15), 0, 0, 360, (255, 255, 255), -1)  # Eye white
    cv2.ellipse(img, (290, 260), (12, 12), 0, 0, 360, (139, 69, 19), -1)     # Iris
    cv2.ellipse(img, (290, 260), (6, 6), 0, 0, 360, (0, 0, 0), -1)           # Pupil
    cv2.ellipse(img, (290, 255), (3, 2), 0, 0, 360, (255, 255, 255), -1)     # Highlight
    
    # Eyebrows
    cv2.ellipse(img, (210, 240), (30, 8), 0, 0, 180, (80, 50, 20), -1)
    cv2.ellipse(img, (290, 240), (30, 8), 0, 0, 180, (80, 50, 20), -1)
    
    # Nose with shadow
    nose_points = np.array([[250, 280], [240, 310], [250, 320], [260, 310]], np.int32)
    cv2.fillPoly(img, [nose_points], (200, 160, 140))
    cv2.polylines(img, [nose_points], True, (180, 140, 120), 2)
    # Nostrils
    cv2.ellipse(img, (245, 315), (3, 2), 0, 0, 360, (160, 120, 100), -1)
    cv2.ellipse(img, (255, 315), (3, 2), 0, 0, 360, (160, 120, 100), -1)
    
    # Mouth with more detail
    cv2.ellipse(img, (250, 360), (30, 12), 0, 0, 180, (180, 100, 100), -1)  # Mouth
    cv2.ellipse(img, (250, 360), (30, 12), 0, 0, 180, (160, 80, 80), 2)     # Mouth outline
    cv2.line(img, (220, 360), (280, 360), (140, 60, 60), 2)                 # Mouth line
    
    # Add facial features for better detection
    # Cheeks with blush
    cv2.ellipse(img, (180, 320), (20, 15), 0, 0, 360, (210, 170, 150), -1)
    cv2.ellipse(img, (320, 320), (20, 15), 0, 0, 360, (210, 170, 150), -1)
    
    # Chin definition
    cv2.ellipse(img, (250, 420), (40, 25), 0, 0, 180, (210, 170, 150), -1)
    
    # Add some texture/shading
    cv2.ellipse(img, (200, 290), (15, 10), 45, 0, 360, (200, 160, 140), -1)  # Left cheek shadow
    cv2.ellipse(img, (300, 290), (15, 10), -45, 0, 360, (200, 160, 140), -1) # Right cheek shadow
    
    filename = 'demo_face.jpg'
    cv2.imwrite(filename, img)
    print(f"✅ Demo face image saved as '{filename}'")
    
    return filename

def test_api_with_demo_image():
    """Test the API with our demo image"""
    print("\n🔍 Testing Face Detection API...")
    
    # Create demo image
    demo_image = create_demo_face_image()
    
    # Convert to base64
    with open(demo_image, 'rb') as f:
        image_data = f.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        image_data_url = f"data:image/jpeg;base64,{image_base64}"
    
    # Test detection
    try:
        payload = {
            'image': image_data_url,
            'method': 'haar'
        }
        
        print("📡 Sending request to face detection API...")
        response = requests.post(
            'http://localhost:5000/api/detect',
            json=payload,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Face Detection API Test Results:")
            print(f"   - Status: {data['success']}")
            print(f"   - Faces detected: {data['face_count']}")
            print(f"   - Method used: {data['method_used']}")
            print(f"   - Response size: {len(response.text)} bytes")
            
            # Save result image
            if 'result_image' in data and data['result_image']:
                result_data = data['result_image'].split(',')[1] if ',' in data['result_image'] else data['result_image']
                result_bytes = base64.b64decode(result_data)
                
                with open('demo_result.jpg', 'wb') as f:
                    f.write(result_bytes)
                print("   - Result image saved as 'demo_result.jpg'")
            
            return True
        else:
            print(f"❌ API test failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API test error: {e}")
        return False

def check_system_status():
    """Check if all components are working"""
    print("🔧 System Status Check")
    print("=" * 50)
    
    # Check backend API
    try:
        response = requests.get('http://localhost:5000/api/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend API: {data['status']} - {data['message']}")
            backend_ok = True
        else:
            print(f"❌ Backend API: HTTP {response.status_code}")
            backend_ok = False
    except:
        print("❌ Backend API: Not responding")
        backend_ok = False
    
    # Check demo file
    demo_file = os.path.abspath('demo.html')
    if os.path.exists(demo_file):
        print(f"✅ Frontend Demo: Available at file:///{demo_file}")
        frontend_ok = True
    else:
        print("❌ Frontend Demo: demo.html not found")
        frontend_ok = False
    
    # Check OpenCV
    try:
        import cv2
        print(f"✅ OpenCV: Version {cv2.__version__}")
        opencv_ok = True
    except:
        print("❌ OpenCV: Not available")
        opencv_ok = False
    
    return backend_ok and frontend_ok and opencv_ok

def main():
    """Main demonstration function"""
    print("🚀 Face Detection Application - Final Demo")
    print("=" * 60)
    
    # Check system status
    if not check_system_status():
        print("\n❌ System check failed. Please ensure all components are running.")
        return
    
    # Test API functionality
    print(f"\n{'='*60}")
    if test_api_with_demo_image():
        print("\n✅ All systems operational!")
    else:
        print("\n❌ API test failed")
        return
    
    # Instructions for user
    print(f"\n{'='*60}")
    print("🎯 Your Face Detection Application is Ready!")
    print("=" * 60)
    
    print("\n📋 How to Use:")
    print("1. 🌐 Open the web interface (demo.html is already open in your browser)")
    print("2. 📷 Upload an image with human faces")
    print("3. ⚙️ Select detection method (Haar Cascades recommended)")
    print("4. 🔍 Click 'Detect Faces' to see results")
    print("5. 📊 View detection statistics and bounding boxes")
    
    print("\n🧪 Quick Test Options in the Web Interface:")
    print("• Click 'Load Sample Face Image' for a quick test")
    print("• Click 'Test with Webcam' to use your camera")
    print("• Drag and drop your own photos")
    
    print("\n🔧 Technical Details:")
    print(f"• Backend API: http://localhost:5000")
    print(f"• Frontend: file:///{os.path.abspath('demo.html')}")
    print(f"• Detection Methods: Haar Cascades, MTCNN (fallback), Dlib (fallback)")
    print(f"• Supported Formats: JPG, PNG, GIF, BMP")
    
    print("\n💡 Tips for Best Results:")
    print("• Use clear, well-lit photos")
    print("• Front-facing faces work better than profiles")
    print("• Ensure faces are at least 50x50 pixels")
    print("• Multiple faces in one image are supported")
    
    print(f"\n{'='*60}")
    print("🎉 Enjoy your AI Face Detection Application!")
    print("=" * 60)

if __name__ == "__main__":
    main()
