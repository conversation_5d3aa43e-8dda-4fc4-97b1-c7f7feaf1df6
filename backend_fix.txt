
# Add this to the top of backend/app.py after other imports
import numpy as np

def convert_numpy_types(data):
    """Convert NumPy types to Python native types for JSON serialization"""
    if isinstance(data, dict):
        return {key: convert_numpy_types(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif isinstance(data, np.bool_):
        return bool(data)
    else:
        return data

# In the detect_faces() function, before returning the JSON response:
# Replace this line:
#   return jsonify({
#       'success': True,
#       'detections': detections,
#       'result_image': result_base64,
#       'face_count': len(detections),
#       'method_used': method
#   })

# With this:
response_data = {
    'success': True,
    'detections': detections,
    'result_image': result_base64,
    'face_count': len(detections),
    'method_used': method
}

# Convert NumPy types to Python types
response_data = convert_numpy_types(response_data)

return jsonify(response_data)
