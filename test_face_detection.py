#!/usr/bin/env python3
"""
Test script for face detection API
Creates a simple test image and tests the face detection functionality
"""

import cv2
import numpy as np
import base64
import requests
import json
from PIL import Image, ImageDraw, ImageFont
import io

def create_test_image_with_face():
    """Create a simple test image with a face-like pattern"""
    # Create a 400x400 white image
    img = Image.new('RGB', (400, 400), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw a simple face
    # Head (circle)
    draw.ellipse([150, 100, 250, 200], fill='peachpuff', outline='black', width=2)
    
    # Eyes
    draw.ellipse([170, 130, 180, 140], fill='black')  # Left eye
    draw.ellipse([220, 130, 230, 140], fill='black')  # Right eye
    
    # Nose
    draw.polygon([(200, 150), (195, 165), (205, 165)], fill='pink', outline='black')
    
    # Mouth
    draw.arc([185, 170, 215, 185], start=0, end=180, fill='red', width=3)
    
    # Convert PIL image to OpenCV format
    opencv_img = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
    
    return opencv_img

def image_to_base64(image):
    """Convert OpenCV image to base64 string"""
    _, buffer = cv2.imencode('.jpg', image)
    image_base64 = base64.b64encode(buffer).decode('utf-8')
    return f"data:image/jpeg;base64,{image_base64}"

def test_face_detection_api():
    """Test the face detection API"""
    print("🤖 Testing Face Detection API...")
    print("=" * 50)
    
    # Create test image
    print("📷 Creating test image with face...")
    test_image = create_test_image_with_face()
    
    # Save test image for reference
    cv2.imwrite('test_face_image.jpg', test_image)
    print("✅ Test image saved as 'test_face_image.jpg'")
    
    # Convert to base64
    image_base64 = image_to_base64(test_image)
    
    # Test different detection methods
    methods = ['haar', 'mtcnn', 'dlib']
    
    for method in methods:
        print(f"\n🔍 Testing {method.upper()} detection method...")
        
        try:
            # Prepare request
            payload = {
                'image': image_base64,
                'method': method
            }
            
            # Send request to API
            response = requests.post(
                'http://localhost:5000/api/detect',
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data['success']:
                    face_count = data['face_count']
                    method_used = data['method_used']
                    
                    print(f"✅ {method.upper()} Detection Results:")
                    print(f"   - Faces detected: {face_count}")
                    print(f"   - Method used: {method_used}")
                    print(f"   - Detections: {len(data.get('detections', []))}")
                    
                    # Save result image if available
                    if 'result_image' in data and data['result_image']:
                        result_filename = f'result_{method}.jpg'
                        # Decode base64 image
                        image_data = data['result_image'].split(',')[1] if ',' in data['result_image'] else data['result_image']
                        image_bytes = base64.b64decode(image_data)
                        
                        with open(result_filename, 'wb') as f:
                            f.write(image_bytes)
                        print(f"   - Result saved as '{result_filename}'")
                    
                    # Print detection details
                    if 'detections' in data:
                        for i, detection in enumerate(data['detections']):
                            bbox = detection['bbox']
                            confidence = detection['confidence']
                            print(f"   - Face {i+1}: bbox={bbox}, confidence={confidence:.2f}")
                else:
                    print(f"❌ {method.upper()} Detection failed: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP Error {response.status_code}: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection failed. Make sure the backend server is running on http://localhost:5000")
            break
        except Exception as e:
            print(f"❌ Error testing {method}: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Face Detection Test Complete!")
    print("\n📋 Next steps:")
    print("1. Open the demo.html file in your browser")
    print("2. Upload an image with faces")
    print("3. Select a detection method")
    print("4. Click 'Detect Faces' to see results")

def test_api_health():
    """Test if the API is healthy"""
    try:
        response = requests.get('http://localhost:5000/api/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Health: {data['status']} - {data['message']}")
            return True
        else:
            print(f"❌ API Health Check Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Face Detection Test Suite")
    print("=" * 50)
    
    # Test API health first
    if test_api_health():
        # Run face detection tests
        test_face_detection_api()
    else:
        print("\n❌ Backend server is not running!")
        print("Please start the backend server first:")
        print("   cd backend")
        print("   python app.py")
